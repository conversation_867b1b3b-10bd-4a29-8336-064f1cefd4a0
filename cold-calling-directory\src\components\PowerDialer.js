export class PowerDialer {
  constructor(companies, onCompanyUpdate, onBackToDirectory, startIndex = 0) {
    this.companies = companies;
    this.currentIndex = startIndex;
    this.onCompanyUpdate = onCompanyUpdate;
    this.onBackToDirectory = onBackToDirectory;

    // Bind event handlers once
    this.boundHandleGvClick = this.handleGvClick.bind(this);
    this.boundHandleKeyboard = this.handleKeyboard.bind(this);
  }

  render() {
    // ... (rest of the code remains the same)
  }

  attachEventListeners() {
    // Back to directory
    document.getElementById('backToDirectory')?.addEventListener('click', () => this.onBackToDirectory());

    // Navigation
    document.getElementById('prevLead')?.addEventListener('click', () => this.previousLead());
    document.getElementById('nextLead')?.addEventListener('click', () => this.nextLead());

    // Disposition buttons
    document.querySelectorAll('.disposition-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const disposition = e.target.dataset.disposition;
        this.updateCurrentDisposition(disposition);
      });
    });

    // Click to edit fields
    document.querySelectorAll('.field-display').forEach(display => {
      display.addEventListener('click', (e) => {
        this.enableFieldEdit(e.target.dataset.field);
      });
    });

    // Save on blur or Enter
    document.querySelectorAll('.field-input').forEach(input => {
      input.addEventListener('blur', (e) => this.saveField(e.target.dataset.field, e.target.value));
      input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') e.target.blur();
        if (e.key === 'Escape') this.cancelFieldEdit(e.target.dataset.field);
      });
    });

    // Auto-save notes on input
    document.getElementById('currentLeadNotes')?.addEventListener('input', () => this.autoSaveNotes());

    // Next lead button
    document.querySelector('.next-btn')?.addEventListener('click', () => this.nextLead());

    // Add document-level event listeners
    document.addEventListener('keydown', this.boundHandleKeyboard);
    document.addEventListener('click', this.boundHandleGvClick, true);
  }

  cleanupEventListeners() {
    document.removeEventListener('keydown', this.boundHandleKeyboard);
    document.removeEventListener('click', this.boundHandleGvClick, true);
  }

  handleGvClick(e) {
    const gvLink = e.target.closest('a.gv-tel-link');

    if (gvLink && gvLink.href && gvLink.href.includes('voice.google.com/calls')) {
      e.preventDefault();
      e.stopPropagation();

      // Open Google Voice in a larger, centered popup window
      const popupWidth = 600;
      const popupHeight = 600;
      const left = (window.screen.width - popupWidth) / 2;
      const top = (window.screen.height - popupHeight) / 4; // Position it in the upper part of the screen

      const popup = window.open(
        gvLink.href,
        'googleVoiceCall',
        `width=${popupWidth},height=${popupHeight},top=${top},left=${left},scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no`
      );

      if (popup) {
        popup.focus();
      }

      // Track the click
      let currentClicks = parseInt(localStorage.getItem('googleVoiceClicks') || '0', 10);
      currentClicks++;
      localStorage.setItem('googleVoiceClicks', currentClicks.toString());

      return false;
    }
  }
}
