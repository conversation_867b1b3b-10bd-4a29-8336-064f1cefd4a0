<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Voice Link Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
        table { border-collapse: collapse; width: 100%; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .gv-tel-link { color: blue; text-decoration: underline; cursor: pointer; }
        .test-link { margin: 5px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>Google Voice Link Test</h1>
    
    <div class="test-section">
        <h2>Phone Number Formatting Test</h2>
        <button onclick="testPhoneFormatting()">Test Phone Formatting</button>
        <div id="formatResult"></div>
    </div>

    <div class="test-section">
        <h2>Google Voice Link Generation</h2>
        <button onclick="testLinkGeneration()">Generate Test Links</button>
        <div id="linkResult"></div>
    </div>

    <div class="test-section">
        <h2>Live Link Test</h2>
        <p>Click these links to test the popup behavior:</p>
        <div id="liveLinks"></div>
        <div class="test-section">
            <h2>Click Tracking</h2>
            <p>Button Clicks: <span id="clickCount">0</span></p>
        </div>
    </div>

    <script>
        function formatPhoneForGoogleVoice(phone) {
            if (!phone) return null
            
            // Remove all non-digit characters
            const digits = phone.replace(/\D/g, '')
            
            // If it's 10 digits, add +1 prefix for US numbers
            if (digits.length === 10) {
                return `+1${digits}`
            }
            
            // If it's 11 digits and starts with 1, format as +1XXXXXXXXXX
            if (digits.length === 11 && digits.startsWith('1')) {
                return `+${digits}`
            }
            
            // If it already has + prefix, return as-is
            if (phone.startsWith('+')) {
                return phone.replace(/\D/g, '').replace(/^/, '+')
            }
            
            // Default: assume it's a US number and add +1
            return `+1${digits}`
        }

        function createGoogleVoiceLink(phone) {
            if (!phone) return ''
            
            const formattedPhone = formatPhoneForGoogleVoice(phone)
            if (!formattedPhone) return ''
            
            // URL encode the phone number for Google Voice
            const encodedPhone = encodeURIComponent(formattedPhone)
            const googleVoiceUrl = `http://voice.google.com/calls?a=nc,${encodedPhone}`
            
            return `<a href="${googleVoiceUrl}" class="gv-tel-link call-link" target="_blank" rel="noopener" title="Call ${formattedPhone} via Google Voice">📞 Call ${formattedPhone} via Google Voice</a>`
        }

        function testPhoneFormatting() {
            const testPhones = [
                "(*************",
                "******-643-9440", 
                "************",
                "16029039982",
                "6029039982",
                "(*************",
                "+14805550123",
                "************",
                "************"
            ];

            let result = '<div class="success">✓ Phone Formatting Test Results:</div>';
            result += '<table><tr><th>Original Phone</th><th>Formatted for Google Voice</th><th>Expected Format</th></tr>';

            testPhones.forEach(phone => {
                const formatted = formatPhoneForGoogleVoice(phone);
                const expected = phone.replace(/\D/g, '').length === 10 ? 
                    `+1${phone.replace(/\D/g, '')}` : 
                    `+${phone.replace(/\D/g, '')}`;

                result += `<tr>`;
                result += `<td>${phone}</td>`;
                result += `<td>${formatted}</td>`;
                result += `<td>${expected}</td>`;
                result += `</tr>`;
            });

            result += '</table>';
            document.getElementById('formatResult').innerHTML = result;
        }

        function testLinkGeneration() {
            const testPhones = [
                "(*************",
                "(*************", 
                "(*************"
            ];

            let result = '<div class="success">✓ Google Voice Link Generation:</div>';
            
            testPhones.forEach(phone => {
                const link = createGoogleVoiceLink(phone);
                result += `<div class="test-link">`;
                result += `<strong>Phone:</strong> ${phone}<br>`;
                result += `<strong>Generated Link:</strong> ${link}`;
                result += `</div>`;
            });

            document.getElementById('linkResult').innerHTML = result;
        }

        function generateLiveLinks() {
            const testPhones = [
                "(*************",
                "(*************", 
                "(*************"
            ];

            let result = '<div class="success">Click these links to test popup behavior:</div>';
            
            testPhones.forEach(phone => {
                const formattedPhone = formatPhoneForGoogleVoice(phone);
                const encodedPhone = encodeURIComponent(formattedPhone);
                const googleVoiceUrl = `http://voice.google.com/calls?a=nc,${encodedPhone}`;
                
                result += `<div class="test-link">`;
                result += `<a href="${googleVoiceUrl}" class="gv-tel-link" target="_blank" rel="noopener" title="Call ${formattedPhone} via Google Voice">📞 Call ${phone} via Google Voice</a>`;
                result += `<br><small>URL: ${googleVoiceUrl}</small>`;
                result += `</div>`;
            });

            document.getElementById('liveLinks').innerHTML = result;
        }

        // Setup Google Voice interception for popup
        let clickCount = 0;

        function setupGoogleVoiceInterception() {
            document.addEventListener('click', (e) => {
                const gvLink = e.target.closest('a.gv-tel-link');

                if (gvLink && gvLink.href && gvLink.href.includes('voice.google.com/calls')) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Open Google Voice in a small popup window
                    const popup = window.open(
                        gvLink.href,
                        'googleVoiceCall',
                        'width=500,height=400,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
                    );

                    // Focus the popup if it opened successfully
                    if (popup) {
                        popup.focus();
                    }

                    // Track the click
                    clickCount++;
                    document.getElementById('clickCount').innerText = clickCount;

                    return false;
                }
            }, true);
        }

        // Auto-run tests and setup on page load
        window.onload = function() {
            testPhoneFormatting();
            testLinkGeneration();
            generateLiveLinks();
            setupGoogleVoiceInterception();
        }
    </script>
</body>
</html>
